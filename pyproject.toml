[tool.ruff]
target-version = "py39"
line-length = 119

[tool.ruff.lint]
ignore = [
    "B028", # warning without explicit stacklevel
    "C408", # dict() calls (stylistic)
    "C901", # function complexity
    "E501",
]
extend-select = ["E", "F", "I", "W", "UP", "B", "T", "C"]

[tool.ruff.lint.per-file-ignores]
# Allow prints in auxiliary scripts
"examples/**.py" = ["T201"]
"scripts/**.py" = ["T201"]
# Ignore import violations in all `__init__.py` files.
"__init__.py" = ["F401"]

[tool.ruff.lint.isort]
lines-after-imports = 2
known-first-party = ["trl"]

[tool.pytest.ini_options]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "low-priority: marks tests as low priority (deselect with '-m \"not low-priority\"')"
]
