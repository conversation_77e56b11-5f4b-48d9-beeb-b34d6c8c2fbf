name: Tests latest TRL release with dev dependencies

on:
  schedule:
    - cron: '0 0 * * *'  # Runs daily at midnight UTC

  workflow_dispatch:

env:
  TQDM_DISABLE: 1
  CI_SLACK_CHANNEL: ${{ secrets.CI_PUSH_MAIN_CHANNEL }}

jobs:
  tests:
    name: Tests latest TRL release with dev dependencies
    runs-on:
      group: aws-g4dn-2xlarge
    container:
      image: pytorch/pytorch:2.6.0-cuda12.6-cudnn9-devel
      options: --gpus all
    defaults:
      run:
        shell: bash
    steps:
      - name: Git checkout
        uses: actions/checkout@v4
        with: { ref: v0.22-release }

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Make and Git
        run: |
          apt-get update && apt-get install -y make git curl

      - name: Install uv
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Create Python virtual environment
        run: |
          uv venv
          uv pip install --upgrade setuptools wheel

      - name: Install dependencies
        run: |
          source .venv/bin/activate
          uv pip install ".[dev]"
          uv pip install -U git+https://github.com/huggingface/accelerate.git
          uv pip install -U git+https://github.com/huggingface/datasets.git
          uv pip install -U git+https://github.com/huggingface/transformers.git

      - name: Test with pytest
        run: |
          source .venv/bin/activate
          make test

      - name: Post to Slack
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: Results of latest TRL with Python 3.12 and dev dependencies
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}
