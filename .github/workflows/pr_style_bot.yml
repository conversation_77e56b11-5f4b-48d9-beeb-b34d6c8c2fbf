name: PR Style Bot

on:
  workflow_dispatch:


permissions:
  contents: write
  pull-requests: write

jobs:
  run-style-bot:
    if: >
      contains(github.event.comment.body, '@bot /style') &&
      github.event.issue.pull_request != null
    runs-on: ubuntu-latest

    steps:
      - name: Extract PR details
        id: pr_info
        uses: actions/github-script@v6
        with:
          script: |
            const prNumber = context.payload.issue.number;
            const { data: pr } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: prNumber
            });
            
            // We capture both the branch ref and the "full_name" of the head repo
            // so that we can check out the correct repository & branch (including forks).
            core.setOutput("prNumber", prNumber);
            core.setOutput("headRef", pr.head.ref);
            core.setOutput("headRepoFullName", pr.head.repo.full_name);

      - name: Check out PR branch
        uses: actions/checkout@v3
        env: 
          HEADREPOFULLNAME: ${{ steps.pr_info.outputs.headRepoFullName }}
          HEADREF: ${{ steps.pr_info.outputs.headRef }}
        with:
          # Instead of checking out the base repo, use the contributor's repo name
          repository: ${{ env.HEADREPOFULLNAME }}
          ref: ${{ env.HEADREF }}
          # You may need fetch-depth: 0 for being able to push
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Debug
        env: 
          HEADREPOFULLNAME: ${{ steps.pr_info.outputs.headRepoFullName }}
          HEADREF: ${{ steps.pr_info.outputs.headRef }}
          PRNUMBER: ${{ steps.pr_info.outputs.prNumber }}
        run: |
          echo "PR number: ${{ env.PRNUMBER }}"
          echo "Head Ref: ${{ env.HEADREF }}"
          echo "Head Repo Full Name: ${{ env.HEADREPOFULLNAME }}"

      - name: Set up Python
        uses: actions/setup-python@v4

      - name: Install dependencies
        run: |
          pip install ruff pre-commit

      - name: Download Makefile from main branch
        run: |
          curl -o main_Makefile https://raw.githubusercontent.com/huggingface/trl/main/Makefile
        
      - name: Compare Makefiles
        run: |
          if ! diff -q main_Makefile Makefile; then
            echo "Error: The Makefile has changed. Please ensure it matches the main branch."
            exit 1
          fi
          echo "No changes in Makefile. Proceeding..."
          rm -rf main_Makefile

      - name: Run make style and make quality
        run: |
          make precommit || true

      - name: Commit and push changes
        id: commit_and_push
        env: 
          HEADREPOFULLNAME: ${{ steps.pr_info.outputs.headRepoFullName }}
          HEADREF: ${{ steps.pr_info.outputs.headRef }}
          PRNUMBER: ${{ steps.pr_info.outputs.prNumber }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo "HEADREPOFULLNAME: ${{ env.HEADREPOFULLNAME }}, HEADREF: ${{ env.HEADREF }}"
          # Configure git with the Actions bot user
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          # Make sure your 'origin' remote is set to the contributor's fork
          git remote set-url origin "https://x-access-token:${GITHUB_TOKEN}@github.com/${{ env.HEADREPOFULLNAME }}.git"

          # If there are changes after running style/quality, commit them
          if [ -n "$(git status --porcelain)" ]; then
            git add .
            git commit -m "Apply style fixes"
            # Push to the original contributor's forked branch
            git push origin HEAD:${{ env.HEADREF }}
            echo "changes_pushed=true" >> $GITHUB_OUTPUT
          else
            echo "No changes to commit."
            echo "changes_pushed=false" >> $GITHUB_OUTPUT
          fi

      - name: Comment on PR with workflow run link
        if: steps.commit_and_push.outputs.changes_pushed == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const prNumber = parseInt(process.env.prNumber, 10);
            const runUrl = `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`

            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: `Style fixes have been applied. [View the workflow run here](${runUrl}).`
            });
        env:
          prNumber: ${{ steps.pr_info.outputs.prNumber }}
