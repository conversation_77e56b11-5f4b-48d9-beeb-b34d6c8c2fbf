name: Publish to PyPI

on:
  push:
    branches:
      - main
      - v*-release
    paths:
      - "VERSION"

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Read version
        id: get_version
        run: echo "version=$(cat VERSION)" >> $GITHUB_OUTPUT

      - name: Debug - Show version.txt content
        run: echo "Version is ${{ steps.get_version.outputs.version }}"

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build twine

      - name: Build package
        run: python -m build

      - name: Publish to PyPI
        if: ${{ !contains(steps.get_version.outputs.version, 'dev') }}
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_TOKEN }}
        run: |
          python -m twine upload dist/*
